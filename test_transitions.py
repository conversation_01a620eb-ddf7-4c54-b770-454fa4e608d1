from manim import *

class TestTransitions(Scene):
    """Prueba de las transiciones suaves"""
    
    def construct(self):
        # Configurar fuente
        self.font_name = "Cera Round Pro"
        
        # Probar las primeras 3 secciones
        self.introduction()
        self.why_it_matters()
        self.test_section()
    
    def create_text(self, text, **kwargs):
        """Método auxiliar para crear texto con fuente personalizada"""
        return Text(text, font=self.font_name, **kwargs)
    
    def smooth_transition(self):
        """Transición suave entre secciones con fade out y pausa"""
        # Fade out de todos los objetos en pantalla
        if self.mobjects:
            self.play(
                *[FadeOut(mob) for mob in self.mobjects],
                run_time=1
            )
        
        # Pausa de 1 segundo
        self.wait(1)
    
    def introduction(self):
        """Introducción con título y efecto de zoom con desvanecimiento"""
        
        # Crear título principal centrado
        title = self.create_text(
            "PEMDAS/BODMAS",
            font_size=64,
            color=WHITE
        )
        title.move_to(ORIGIN)
        
        # Animación de entrada del título
        self.play(
            Write(title),
            run_time=2
        )
        
        # Esperar 1 segundo
        self.wait(1)
        
        # Efecto de zoom con desvanecimiento (1 segundo)
        self.play(
            title.animate.scale(3).set_opacity(0),
            run_time=1
        )
        
        # Pausa de 1 segundo antes de la siguiente sección
        self.wait(1)
    
    def why_it_matters(self):
        """Muestra por qué es importante PEMDAS/BODMAS en la vida real"""
        
        # Título de la sección
        section_title = self.create_text(
            "¿Por Qué Nos Importa PEMDAS/BODMAS?",
            font_size=40,
            color=WHITE
        )
        section_title.to_edge(UP)
        
        self.play(Write(section_title), run_time=1)
        
        # Situaciones cotidianas
        situations = [
            "Calcular descuentos",
            "Dividir cuentas",
            "Planificar presupuesto",
            "Fórmulas financieras"
        ]
        
        # Crear y animar cada situación
        for i, situation in enumerate(situations):
            situation_text = self.create_text(
                situation,
                font_size=28,
                color=GRAY
            )
            situation_text.shift(UP * (1 - i * 0.8))
            
            self.play(
                FadeIn(situation_text),
                run_time=0.8
            )
            
            if i < len(situations) - 1:
                self.wait(0.5)
        
        self.wait(2)
        self.smooth_transition()
    
    def test_section(self):
        """Sección de prueba final"""
        
        final_text = self.create_text(
            "¡Transiciones Suaves Funcionando!",
            font_size=48,
            color=GREEN
        )
        
        self.play(Write(final_text), run_time=2)
        self.wait(2)
        
        # Fade out final
        self.play(FadeOut(final_text), run_time=1)
        self.wait(1)

if __name__ == "__main__":
    config.pixel_height = 1080
    config.pixel_width = 1920
    config.frame_rate = 60
    
    scene = TestTransitions()
    scene.render()
