from manim import *
import numpy as np

# Configuración global para Full HD 60fps
config.frame_width = 16
config.frame_height = 9
config.pixel_width = 1920
config.pixel_height = 1080
config.frame_rate = 60

class PEMDASBODMASAnimacion(Scene):
    def construct(self):
        # Configurar el color de fondo
        self.camera.background_color = "#0F0F23"

        # Ejecutar todas las secciones de la animación
        self.introduccion_entusiasta()
        self.explicacion_pemdas_bodmas()
        self.ejemplo_1_operaciones_basicas()
        self.ejemplo_2_prioridad_multiplicacion()
        self.ejemplo_3_poder_parentesis()
        self.ejemplo_4_exponentes_en_accion()
        self.ejemplo_5_desafio_complejo()
        self.mini_concurso_interactivo()
        self.cierre_triunfante()

    def introduccion_entusiasta(self):
        """Introducción entusiasta de 1 minuto"""
        self.clear()

        # Título principal animado
        titulo = Text(
            "¡Hola, genios de las matemáticas!",
            font_size=48,
            color=YELLOW,
            weight=BOLD
        )

        subtitulo = Text(
            "¿Listos para convertirnos en los reyes y reinas de PEMDAS/BODMAS?",
            font_size=36,
            color=WHITE
        ).next_to(titulo, DOWN, buff=0.5)

        # Animación de entrada del título
        self.play(
            Write(titulo, run_time=2),
            rate_func=smooth
        )
        self.play(
            FadeIn(subtitulo, shift=UP),
            run_time=1.5
        )

        # Corona animada
        corona = self.crear_corona().scale(0.8).to_edge(UP, buff=1)

        # Libro de matemáticas
        libro = self.crear_libro_matematicas().scale(0.6).to_edge(DOWN, buff=1)

        self.play(
            DrawBorderThenFill(corona),
            DrawBorderThenFill(libro),
            run_time=2
        )

        # Mensaje de superpoder
        superpoder_text = Text(
            "Hoy desbloquearemos un superpoder matemático",
            font_size=32,
            color=BLUE,
            weight=BOLD
        ).move_to(ORIGIN)

        # Efecto de brillo
        self.play(
            FadeOut(titulo),
            FadeOut(subtitulo),
            run_time=1
        )

        self.play(
            Write(superpoder_text),
            corona.animate.scale(1.2).set_color(GOLD),
            run_time=2
        )

        # Efectos de partículas brillantes
        particulas = self.crear_particulas_brillantes()
        self.play(
            *[FadeIn(p, shift=UP*0.5) for p in particulas],
            run_time=1
        )

        self.wait(2)
        self.clear()

    def explicacion_pemdas_bodmas(self):
        """Explicación de PEMDAS/BODMAS de 2 minutos"""

        # Título de la sección
        titulo_seccion = Text(
            "El Orden Mágico de las Operaciones",
            font_size=44,
            color=PURPLE,
            weight=BOLD
        ).to_edge(UP, buff=0.5)

        self.play(Write(titulo_seccion), run_time=1.5)

        # PEMDAS y BODMAS lado a lado
        pemdas_title = Text("PEMDAS", font_size=40, color=RED, weight=BOLD)
        bodmas_title = Text("BODMAS", font_size=40, color=BLUE, weight=BOLD)

        pemdas_title.move_to(LEFT * 3 + UP * 1)
        bodmas_title.move_to(RIGHT * 3 + UP * 1)

        self.play(
            Write(pemdas_title),
            Write(bodmas_title),
            run_time=1.5
        )

        # Explicaciones detalladas
        explicaciones_pemdas = [
            ("P", "Paréntesis", YELLOW, "( )"),
            ("E", "Exponentes", GREEN, "x²"),
            ("M", "Multiplicación", ORANGE, "×"),
            ("D", "División", ORANGE, "÷"),
            ("A", "Adición", PINK, "+"),
            ("S", "Sustracción", PINK, "-")
        ]

        explicaciones_bodmas = [
            ("B", "Brackets", YELLOW, "[ ]"),
            ("O", "Orders", GREEN, "x³"),
            ("M", "Multiplicación", ORANGE, "×"),
            ("D", "División", ORANGE, "÷"),
            ("A", "Adición", PINK, "+"),
            ("S", "Sustracción", PINK, "-")
        ]

        # Crear y animar explicaciones
        self.animar_explicaciones_operaciones(explicaciones_pemdas, LEFT * 3)
        self.animar_explicaciones_operaciones(explicaciones_bodmas, RIGHT * 3)

        # Flecha de dirección para MD y AS
        flecha_md = Arrow(LEFT * 2, RIGHT * 2, color=ORANGE, stroke_width=6)
        flecha_as = Arrow(LEFT * 2, RIGHT * 2, color=PINK, stroke_width=6)

        flecha_md.next_to(pemdas_title, DOWN, buff=2)
        flecha_as.next_to(flecha_md, DOWN, buff=0.5)

        texto_direccion = Text(
            "¡De izquierda a derecha!",
            font_size=28,
            color=WHITE,
            weight=BOLD
        ).next_to(flecha_as, DOWN, buff=0.5)

        self.play(
            GrowArrow(flecha_md),
            GrowArrow(flecha_as),
            Write(texto_direccion),
            run_time=2
        )

        self.wait(3)
        self.clear()

    def animar_explicaciones_operaciones(self, explicaciones, posicion_base):
        """Helper para animar las explicaciones de operaciones"""
        for i, (letra, nombre, color, simbolo) in enumerate(explicaciones):
            y_pos = posicion_base[1] - 0.5 - (i * 0.7)

            letra_obj = Text(letra, font_size=32, color=color, weight=BOLD)
            nombre_obj = Text(f": {nombre}", font_size=24, color=WHITE)
            simbolo_obj = Text(simbolo, font_size=28, color=color, weight=BOLD)

            letra_obj.move_to([posicion_base[0] - 1, y_pos, 0])
            nombre_obj.next_to(letra_obj, RIGHT, buff=0.1)
            simbolo_obj.next_to(nombre_obj, RIGHT, buff=0.3)

            # Animación especial para cada letra
            if letra in ["P", "B"]:
                # Paréntesis animados
                self.play(
                    Write(letra_obj),
                    Write(nombre_obj),
                    DrawBorderThenFill(simbolo_obj),
                    run_time=0.8
                )
            elif letra in ["E", "O"]:
                # Exponentes con efecto de potencia
                self.play(
                    Write(letra_obj),
                    Write(nombre_obj),
                    simbolo_obj.animate.scale(1.3).set_color(GREEN),
                    run_time=0.8
                )
            else:
                self.play(
                    Write(letra_obj),
                    Write(nombre_obj),
                    Write(simbolo_obj),
                    run_time=0.6
                )

    def crear_corona(self):
        """Crear una corona decorativa"""
        # Base de la corona
        base = Rectangle(width=2, height=0.3, color=GOLD, fill_opacity=1)

        # Puntas de la corona
        puntas = VGroup()
        for i in range(5):
            x = -0.8 + i * 0.4
            punta = Polygon(
                [x-0.15, 0.15, 0], [x, 0.5, 0], [x+0.15, 0.15, 0],
                color=GOLD, fill_opacity=1
            )
            puntas.add(punta)

        # Joyas en la corona
        joyas = VGroup()
        for i in range(3):
            x = -0.4 + i * 0.4
            joya = Circle(radius=0.08, color=RED, fill_opacity=1)
            joya.move_to([x, 0.3, 0])
            joyas.add(joya)

        corona = VGroup(base, puntas, joyas)
        return corona

    def crear_libro_matematicas(self):
        """Crear un libro de matemáticas decorativo"""
        # Portada del libro
        portada = Rectangle(width=1.5, height=2, color=BLUE, fill_opacity=1)

        # Título del libro
        titulo = Text("MATH", font_size=20, color=WHITE, weight=BOLD)
        titulo.move_to(portada.get_center() + UP * 0.3)

        # Símbolos matemáticos
        simbolos = VGroup(
            Text("+", font_size=16, color=YELLOW),
            Text("×", font_size=16, color=GREEN),
            Text("÷", font_size=16, color=RED),
            Text("=", font_size=16, color=ORANGE)
        )

        simbolos.arrange(RIGHT, buff=0.2)
        simbolos.move_to(portada.get_center() + DOWN * 0.3)

        libro = VGroup(portada, titulo, simbolos)
        return libro

    def crear_particulas_brillantes(self):
        """Crear partículas brillantes para efectos"""
        particulas = VGroup()
        for _ in range(12):
            x = np.random.uniform(-6, 6)
            y = np.random.uniform(-3, 3)
            particula = Star(n=4, outer_radius=0.1, color=YELLOW, fill_opacity=0.8)
            particula.move_to([x, y, 0])
            particulas.add(particula)
        return particulas

    def ejemplo_1_operaciones_basicas(self):
        """Ejemplo 1: Operaciones básicas - 8 - 2 + 3"""
        self.mostrar_titulo_ejemplo("Ejemplo 1: Operaciones Básicas", BLUE)

        # Problema
        problema = MathTex("8 - 2 + 3", font_size=60, color=WHITE)
        problema.move_to(UP * 1.5)

        self.play(Write(problema), run_time=1.5)

        # Explicación
        explicacion = Text(
            "Sin paréntesis ni exponentes, vamos de izquierda a derecha",
            font_size=28,
            color=YELLOW
        ).next_to(problema, DOWN, buff=1)

        self.play(Write(explicacion), run_time=2)

        # Paso 1: Resaltar primera operación
        paso1 = MathTex("8 - 2", " + 3", font_size=60)
        paso1[0].set_color(ORANGE)
        paso1[1].set_color(WHITE)
        paso1.move_to(problema.get_center())

        self.play(Transform(problema, paso1), run_time=1)

        # Flecha y resultado del primer paso
        flecha1 = Arrow(paso1[0].get_bottom(), DOWN * 0.5, color=ORANGE)
        resultado1 = MathTex("6", font_size=50, color=ORANGE)
        resultado1.next_to(flecha1, DOWN, buff=0.2)

        self.play(
            GrowArrow(flecha1),
            Write(resultado1),
            run_time=1.5
        )

        # Paso 2: Nueva expresión
        paso2 = MathTex("6 + 3", font_size=60, color=GREEN)
        paso2.move_to(UP * 0.5)

        self.play(
            Write(paso2),
            FadeOut(flecha1),
            FadeOut(resultado1),
            run_time=1.5
        )

        # Resultado final
        flecha_final = Arrow(paso2.get_bottom(), DOWN * 0.5, color=GREEN)
        resultado_final = MathTex("= 9", font_size=70, color=GREEN)
        resultado_final.next_to(flecha_final, DOWN, buff=0.2)

        self.play(
            GrowArrow(flecha_final),
            Write(resultado_final),
            run_time=2
        )

        # Celebración
        self.celebrar_respuesta_correcta()
        self.wait(2)
        self.clear()

    def ejemplo_2_prioridad_multiplicacion(self):
        """Ejemplo 2: Prioridad de multiplicación - 4 + 2 × 3"""
        self.mostrar_titulo_ejemplo("Ejemplo 2: ¡La Multiplicación Primero!", RED)

        problema = MathTex("4 + 2 \\times 3", font_size=60, color=WHITE)
        problema.move_to(UP * 1.5)

        self.play(Write(problema), run_time=1.5)

        # Recordatorio de PEMDAS
        recordatorio = Text(
            "Recordemos: Multiplicación antes que Adición",
            font_size=28,
            color=YELLOW
        ).next_to(problema, DOWN, buff=1)

        self.play(Write(recordatorio), run_time=2)

        # Resaltar multiplicación
        problema_resaltado = MathTex("4 + ", "2 \\times 3", font_size=60)
        problema_resaltado[0].set_color(WHITE)
        problema_resaltado[1].set_color(RED)
        problema_resaltado.move_to(problema.get_center())

        # Efecto de pulso en la multiplicación
        self.play(
            Transform(problema, problema_resaltado),
            problema_resaltado[1].animate.scale(1.2),
            run_time=1.5
        )

        self.play(problema_resaltado[1].animate.scale(1/1.2), run_time=0.5)

        # Resolver multiplicación
        flecha_mult = Arrow(problema_resaltado[1].get_bottom(), DOWN * 0.5, color=RED)
        resultado_mult = MathTex("6", font_size=50, color=RED)
        resultado_mult.next_to(flecha_mult, DOWN, buff=0.2)

        self.play(
            GrowArrow(flecha_mult),
            Write(resultado_mult),
            run_time=1.5
        )

        # Nueva expresión
        nueva_expr = MathTex("4 + 6", font_size=60, color=GREEN)
        nueva_expr.move_to(UP * 0.5)

        self.play(
            Write(nueva_expr),
            FadeOut(flecha_mult),
            FadeOut(resultado_mult),
            run_time=1.5
        )

        # Resultado final
        flecha_final = Arrow(nueva_expr.get_bottom(), DOWN * 0.5, color=GREEN)
        resultado_final = MathTex("= 10", font_size=70, color=GREEN)
        resultado_final.next_to(flecha_final, DOWN, buff=0.2)

        self.play(
            GrowArrow(flecha_final),
            Write(resultado_final),
            run_time=2
        )

        self.celebrar_respuesta_correcta()
        self.wait(2)
        self.clear()

    def ejemplo_3_poder_parentesis(self):
        """Ejemplo 3: El poder de los paréntesis - (4 + 2) × 3"""
        self.mostrar_titulo_ejemplo("Ejemplo 3: ¡El Poder de los Paréntesis!", PURPLE)

        problema = MathTex("(4 + 2) \\times 3", font_size=60, color=WHITE)
        problema.move_to(UP * 1.5)

        self.play(Write(problema), run_time=1.5)

        # Mensaje sobre paréntesis
        mensaje = Text(
            "¡Los paréntesis son los jefes! Siempre van primero",
            font_size=28,
            color=YELLOW
        ).next_to(problema, DOWN, buff=1)

        self.play(Write(mensaje), run_time=2)

        # Efecto especial en paréntesis
        parentesis_resaltado = MathTex("(4 + 2)", " \\times 3", font_size=60)
        parentesis_resaltado[0].set_color(PURPLE)
        parentesis_resaltado[1].set_color(WHITE)
        parentesis_resaltado.move_to(problema.get_center())

        # Efecto de brillo en paréntesis
        self.play(
            Transform(problema, parentesis_resaltado),
            parentesis_resaltado[0].animate.set_stroke(YELLOW, width=3),
            run_time=1.5
        )

        # Pulso en paréntesis
        for _ in range(3):
            self.play(
                parentesis_resaltado[0].animate.scale(1.1),
                run_time=0.3
            )
            self.play(
                parentesis_resaltado[0].animate.scale(1/1.1),
                run_time=0.3
            )

        # Resolver paréntesis
        flecha_par = Arrow(parentesis_resaltado[0].get_bottom(), DOWN * 0.5, color=PURPLE)
        resultado_par = MathTex("6", font_size=50, color=PURPLE)
        resultado_par.next_to(flecha_par, DOWN, buff=0.2)

        self.play(
            GrowArrow(flecha_par),
            Write(resultado_par),
            run_time=1.5
        )

        # Nueva expresión
        nueva_expr = MathTex("6 \\times 3", font_size=60, color=GREEN)
        nueva_expr.move_to(UP * 0.5)

        self.play(
            Write(nueva_expr),
            FadeOut(flecha_par),
            FadeOut(resultado_par),
            run_time=1.5
        )

        # Resultado final
        flecha_final = Arrow(nueva_expr.get_bottom(), DOWN * 0.5, color=GREEN)
        resultado_final = MathTex("= 18", font_size=70, color=GREEN)
        resultado_final.next_to(flecha_final, DOWN, buff=0.2)

        self.play(
            GrowArrow(flecha_final),
            Write(resultado_final),
            run_time=2
        )

        self.celebrar_respuesta_correcta()
        self.wait(2)
        self.clear()

    def ejemplo_4_exponentes_en_accion(self):
        """Ejemplo 4: Exponentes en acción - 2³ + 5 × 2 - 1"""
        self.mostrar_titulo_ejemplo("Ejemplo 4: ¡Los Exponentes en Acción!", GREEN)

        problema = MathTex("2^3 + 5 \\times 2 - 1", font_size=60, color=WHITE)
        problema.move_to(UP * 1.5)

        self.play(Write(problema), run_time=1.5)

        # Mensaje sobre exponentes
        mensaje = Text(
            "¡Los exponentes son súper poderosos! Van después de los paréntesis",
            font_size=26,
            color=YELLOW
        ).next_to(problema, DOWN, buff=1)

        self.play(Write(mensaje), run_time=2)

        # Paso 1: Resaltar exponente
        paso1 = MathTex("2^3", " + 5 \\times 2 - 1", font_size=60)
        paso1[0].set_color(GREEN)
        paso1[1].set_color(WHITE)
        paso1.move_to(problema.get_center())

        # Efecto especial para exponente
        self.play(
            Transform(problema, paso1),
            paso1[0].animate.scale(1.3),
            run_time=1.5
        )

        # Animación visual del exponente
        cubo_visual = self.crear_animacion_cubo()
        cubo_visual.scale(0.5).next_to(paso1[0], RIGHT, buff=0.5)

        self.play(
            DrawBorderThenFill(cubo_visual),
            paso1[0].animate.scale(1/1.3),
            run_time=2
        )

        # Resolver exponente
        flecha_exp = Arrow(paso1[0].get_bottom(), DOWN * 0.5, color=GREEN)
        resultado_exp = MathTex("8", font_size=50, color=GREEN)
        resultado_exp.next_to(flecha_exp, DOWN, buff=0.2)

        self.play(
            GrowArrow(flecha_exp),
            Write(resultado_exp),
            FadeOut(cubo_visual),
            run_time=1.5
        )

        # Paso 2: Nueva expresión con multiplicación
        paso2 = MathTex("8 + ", "5 \\times 2", " - 1", font_size=60)
        paso2[0].set_color(WHITE)
        paso2[1].set_color(ORANGE)
        paso2[2].set_color(WHITE)
        paso2.move_to(UP * 0.5)

        self.play(
            Write(paso2),
            FadeOut(flecha_exp),
            FadeOut(resultado_exp),
            run_time=1.5
        )

        # Resolver multiplicación
        flecha_mult = Arrow(paso2[1].get_bottom(), DOWN * 0.5, color=ORANGE)
        resultado_mult = MathTex("10", font_size=50, color=ORANGE)
        resultado_mult.next_to(flecha_mult, DOWN, buff=0.2)

        self.play(
            GrowArrow(flecha_mult),
            Write(resultado_mult),
            run_time=1.5
        )

        # Paso 3: Operaciones de izquierda a derecha
        paso3 = MathTex("8 + 10 - 1", font_size=60, color=PINK)
        paso3.move_to(UP * 0.2)

        self.play(
            Write(paso3),
            FadeOut(flecha_mult),
            FadeOut(resultado_mult),
            run_time=1.5
        )

        # Paso 4: Primera suma
        paso4 = MathTex("18 - 1", font_size=60, color=PINK)
        paso4.move_to(DOWN * 0.2)

        self.play(Write(paso4), run_time=1.5)

        # Resultado final
        flecha_final = Arrow(paso4.get_bottom(), DOWN * 0.5, color=GREEN)
        resultado_final = MathTex("= 17", font_size=70, color=GREEN)
        resultado_final.next_to(flecha_final, DOWN, buff=0.2)

        self.play(
            GrowArrow(flecha_final),
            Write(resultado_final),
            run_time=2
        )

        self.celebrar_respuesta_correcta()
        self.wait(2)
        self.clear()

    def ejemplo_5_desafio_complejo(self):
        """Ejemplo 5: Desafío complejo - 12 ÷ (4 - 1) + 3² × 2"""
        self.mostrar_titulo_ejemplo("Ejemplo 5: ¡El Gran Desafío!", GOLD)

        problema = MathTex("12 \\div (4 - 1) + 3^2 \\times 2", font_size=50, color=WHITE)
        problema.move_to(UP * 2)

        self.play(Write(problema), run_time=2)

        # Mensaje de desafío
        mensaje = Text(
            "¡Este es nuestro gran desafío! Usemos todo lo que hemos aprendido",
            font_size=26,
            color=YELLOW
        ).next_to(problema, DOWN, buff=0.5)

        self.play(Write(mensaje), run_time=2)

        # Indicador de orden PEMDAS
        orden_pemdas = Text("P → E → M/D → A/S", font_size=24, color=BLUE)
        orden_pemdas.to_edge(UP, buff=0.5)

        self.play(Write(orden_pemdas), run_time=1)

        # Paso 1: Paréntesis primero
        paso1 = MathTex("12 \\div ", "(4 - 1)", " + 3^2 \\times 2", font_size=50)
        paso1[0].set_color(WHITE)
        paso1[1].set_color(PURPLE)
        paso1[2].set_color(WHITE)
        paso1.move_to(UP * 1)

        self.play(
            Transform(problema, paso1),
            orden_pemdas[0].animate.set_color(PURPLE).scale(1.2),
            run_time=1.5
        )

        # Resolver paréntesis
        resultado_par = MathTex("12 \\div 3 + 3^2 \\times 2", font_size=50, color=WHITE)
        resultado_par.move_to(UP * 0.5)

        self.play(
            Write(resultado_par),
            orden_pemdas[0].animate.set_color(BLUE).scale(1/1.2),
            run_time=1.5
        )

        # Paso 2: Exponentes
        paso2 = MathTex("12 \\div 3 + ", "3^2", " \\times 2", font_size=50)
        paso2[0].set_color(WHITE)
        paso2[1].set_color(GREEN)
        paso2[2].set_color(WHITE)
        paso2.move_to(resultado_par.get_center())

        self.play(
            Transform(resultado_par, paso2),
            orden_pemdas[2].animate.set_color(GREEN).scale(1.2),
            run_time=1.5
        )

        # Resolver exponente
        resultado_exp = MathTex("12 \\div 3 + 9 \\times 2", font_size=50, color=WHITE)
        resultado_exp.move_to(ORIGIN)

        self.play(
            Write(resultado_exp),
            orden_pemdas[2].animate.set_color(BLUE).scale(1/1.2),
            run_time=1.5
        )

        # Paso 3: Multiplicación y División (izquierda a derecha)
        paso3a = MathTex("12 \\div 3", " + 9 \\times 2", font_size=50)
        paso3a[0].set_color(ORANGE)
        paso3a[1].set_color(WHITE)
        paso3a.move_to(DOWN * 0.5)

        self.play(
            Write(paso3a),
            orden_pemdas[4:6].animate.set_color(ORANGE).scale(1.2),
            run_time=1.5
        )

        # Resolver división
        paso3b = MathTex("4 + ", "9 \\times 2", font_size=50)
        paso3b[0].set_color(WHITE)
        paso3b[1].set_color(ORANGE)
        paso3b.move_to(DOWN * 1)

        self.play(Write(paso3b), run_time=1.5)

        # Resolver multiplicación
        paso3c = MathTex("4 + 18", font_size=50, color=PINK)
        paso3c.move_to(DOWN * 1.5)

        self.play(
            Write(paso3c),
            orden_pemdas[4:6].animate.set_color(BLUE).scale(1/1.2),
            orden_pemdas[8:].animate.set_color(PINK).scale(1.2),
            run_time=1.5
        )

        # Resultado final
        flecha_final = Arrow(paso3c.get_bottom(), DOWN * 0.5, color=GREEN)
        resultado_final = MathTex("= 22", font_size=70, color=GREEN)
        resultado_final.next_to(flecha_final, DOWN, buff=0.2)

        self.play(
            GrowArrow(flecha_final),
            Write(resultado_final),
            orden_pemdas[8:].animate.set_color(BLUE).scale(1/1.2),
            run_time=2
        )

        self.celebrar_respuesta_correcta()
        self.wait(3)
        self.clear()

    def mini_concurso_interactivo(self):
        """Mini concurso interactivo de 2 minutos"""
        self.mostrar_titulo_ejemplo("¡Mini Concurso Matemático!", GOLD)

        # Mensaje de introducción
        intro = Text(
            "¡Es hora de poner a prueba tus nuevos superpoderes!",
            font_size=32,
            color=YELLOW,
            weight=BOLD
        ).move_to(UP * 2)

        self.play(Write(intro), run_time=2)

        # Problema 1
        problema1_titulo = Text("Problema 1:", font_size=28, color=BLUE, weight=BOLD)
        problema1_titulo.move_to(UP * 0.5 + LEFT * 3)

        problema1 = MathTex("6 + 4 \\div 2", font_size=48, color=WHITE)
        problema1.next_to(problema1_titulo, DOWN, buff=0.3)

        # Problema 2
        problema2_titulo = Text("Problema 2:", font_size=28, color=RED, weight=BOLD)
        problema2_titulo.move_to(UP * 0.5 + RIGHT * 3)

        problema2 = MathTex("(6 + 4) \\div 2", font_size=48, color=WHITE)
        problema2.next_to(problema2_titulo, DOWN, buff=0.3)

        self.play(
            Write(problema1_titulo),
            Write(problema1),
            Write(problema2_titulo),
            Write(problema2),
            run_time=2
        )

        # Cronómetro
        cronometro = self.crear_cronometro()
        cronometro.to_edge(DOWN, buff=1)

        self.play(DrawBorderThenFill(cronometro), run_time=1)

        # Cuenta regresiva
        for i in range(5, 0, -1):
            numero = Text(str(i), font_size=60, color=RED, weight=BOLD)
            numero.move_to(cronometro.get_center())
            self.play(Write(numero), run_time=0.8)
            self.play(FadeOut(numero), run_time=0.2)

        # ¡Tiempo!
        tiempo_texto = Text("¡TIEMPO!", font_size=48, color=RED, weight=BOLD)
        tiempo_texto.move_to(cronometro.get_center())
        self.play(Write(tiempo_texto), run_time=1)

        self.wait(1)
        self.play(FadeOut(tiempo_texto), run_time=0.5)

        # Revelar respuestas
        respuesta1 = MathTex("= 8", font_size=40, color=GREEN)
        respuesta1.next_to(problema1, DOWN, buff=0.5)

        respuesta2 = MathTex("= 5", font_size=40, color=GREEN)
        respuesta2.next_to(problema2, DOWN, buff=0.5)

        self.play(
            Write(respuesta1),
            Write(respuesta2),
            run_time=2
        )

        # Explicación rápida
        explicacion1 = Text("6 + (4÷2) = 6 + 2 = 8", font_size=20, color=BLUE)
        explicacion1.next_to(respuesta1, DOWN, buff=0.2)

        explicacion2 = Text("(6+4)÷2 = 10÷2 = 5", font_size=20, color=RED)
        explicacion2.next_to(respuesta2, DOWN, buff=0.2)

        self.play(
            Write(explicacion1),
            Write(explicacion2),
            run_time=2
        )

        # Celebración virtual
        self.celebracion_virtual()
        self.wait(2)
        self.clear()

    def cierre_triunfante(self):
        """Cierre triunfante de 1 minuto"""
        # Fuegos artificiales
        fuegos = self.crear_fuegos_artificiales()

        self.play(
            *[FadeIn(fuego, shift=UP*2) for fuego in fuegos],
            run_time=2
        )

        # Mensaje principal
        mensaje_principal = Text(
            "¡FELICITACIONES!",
            font_size=60,
            color=GOLD,
            weight=BOLD
        ).move_to(UP * 1.5)

        self.play(Write(mensaje_principal), run_time=2)

        # Submensaje
        submensaje = Text(
            "¡Ya eres un maestro de PEMDAS/BODMAS!",
            font_size=36,
            color=YELLOW
        ).next_to(mensaje_principal, DOWN, buff=0.5)

        self.play(Write(submensaje), run_time=1.5)

        # Corona final
        corona_final = self.crear_corona().scale(1.2)
        corona_final.next_to(submensaje, DOWN, buff=1)

        self.play(DrawBorderThenFill(corona_final), run_time=2)

        # Mensaje motivacional
        motivacion = Text(
            "¡Sigue practicando y serás imparable en matemáticas!",
            font_size=28,
            color=WHITE
        ).next_to(corona_final, DOWN, buff=1)

        self.play(Write(motivacion), run_time=2)

        # Call to action
        call_to_action = Text(
            "¡Hasta la próxima aventura matemática!",
            font_size=24,
            color=BLUE,
            weight=BOLD
        ).to_edge(DOWN, buff=1)

        self.play(Write(call_to_action), run_time=1.5)

        # Efecto final de partículas
        particulas_finales = self.crear_particulas_brillantes()
        self.play(
            *[FadeIn(p, shift=UP*0.5) for p in particulas_finales],
            run_time=2
        )

        self.wait(3)

    # Métodos auxiliares
    def mostrar_titulo_ejemplo(self, titulo, color):
        """Mostrar título de ejemplo con animación"""
        titulo_obj = Text(titulo, font_size=40, color=color, weight=BOLD)
        titulo_obj.to_edge(UP, buff=0.5)

        # Línea decorativa
        linea = Line(LEFT * 6, RIGHT * 6, color=color, stroke_width=4)
        linea.next_to(titulo_obj, DOWN, buff=0.2)

        self.play(
            Write(titulo_obj),
            GrowFromCenter(linea),
            run_time=1.5
        )

        self.wait(0.5)

    def celebrar_respuesta_correcta(self):
        """Animación de celebración para respuesta correcta"""
        # Estrellas de celebración
        estrellas = VGroup()
        for _ in range(8):
            x = np.random.uniform(-5, 5)
            y = np.random.uniform(-2, 2)
            estrella = Star(n=5, outer_radius=0.2, color=YELLOW, fill_opacity=0.8)
            estrella.move_to([x, y, 0])
            estrellas.add(estrella)

        # Texto de celebración
        celebracion = Text("¡EXCELENTE!", font_size=36, color=GREEN, weight=BOLD)
        celebracion.move_to(UP * 3)

        self.play(
            *[FadeIn(estrella, shift=UP*0.3) for estrella in estrellas],
            Write(celebracion),
            run_time=1.5
        )

        self.play(
            *[FadeOut(estrella) for estrella in estrellas],
            FadeOut(celebracion),
            run_time=1
        )

    def crear_animacion_cubo(self):
        """Crear animación visual para exponente al cubo"""
        # Crear un cubo 3D simple
        cubo = VGroup()

        # Cara frontal
        cara_frontal = Square(side_length=1, color=BLUE, fill_opacity=0.7)

        # Cara superior (perspectiva)
        cara_superior = Polygon(
            [0, 1, 0], [0.3, 1.3, 0], [1.3, 1.3, 0], [1, 1, 0],
            color="#87CEEB", fill_opacity=0.5
        )

        # Cara derecha (perspectiva)
        cara_derecha = Polygon(
            [1, 0, 0], [1.3, 0.3, 0], [1.3, 1.3, 0], [1, 1, 0],
            color="#00008B", fill_opacity=0.6
        )

        cubo.add(cara_frontal, cara_superior, cara_derecha)

        # Etiquetas 2×2×2
        etiqueta = Text("2×2×2", font_size=16, color=WHITE)
        etiqueta.move_to(cara_frontal.get_center())

        cubo.add(etiqueta)
        return cubo

    def crear_cronometro(self):
        """Crear un cronómetro visual"""
        # Círculo del cronómetro
        circulo = Circle(radius=0.8, color=WHITE, stroke_width=4)

        # Números del cronómetro
        numeros = VGroup()
        for i in range(1, 6):
            angulo = i * TAU / 5 - TAU / 4  # Empezar desde arriba
            x = 0.6 * np.cos(angulo)
            y = 0.6 * np.sin(angulo)
            numero = Text(str(i), font_size=24, color=WHITE)
            numero.move_to([x, y, 0])
            numeros.add(numero)

        cronometro = VGroup(circulo, numeros)
        return cronometro

    def celebracion_virtual(self):
        """Celebración virtual con efectos especiales"""
        # Confeti
        confeti = VGroup()
        for _ in range(20):
            x = np.random.uniform(-6, 6)
            y = np.random.uniform(2, 4)
            color = np.random.choice([RED, BLUE, GREEN, YELLOW, PURPLE])
            pieza = Rectangle(width=0.1, height=0.2, color=color, fill_opacity=1)
            pieza.move_to([x, y, 0])
            confeti.add(pieza)

        # Animación de caída del confeti
        self.play(
            *[pieza.animate.shift(DOWN * 6).rotate(PI) for pieza in confeti],
            run_time=3
        )

        # Texto de celebración
        celebracion = Text("¡INCREÍBLE!", font_size=48, color=GOLD, weight=BOLD)
        celebracion.move_to(ORIGIN)

        self.play(Write(celebracion), run_time=1)
        self.play(FadeOut(celebracion), run_time=1)

    def crear_fuegos_artificiales(self):
        """Crear fuegos artificiales para el final"""
        fuegos = VGroup()

        for _ in range(6):
            x = np.random.uniform(-5, 5)
            y = np.random.uniform(1, 3)
            color = np.random.choice([RED, BLUE, GREEN, YELLOW, PURPLE, ORANGE])

            # Crear explosión de fuego artificial
            explosion = VGroup()
            for i in range(12):
                angulo = i * TAU / 12
                linea = Line(
                    ORIGIN,
                    [0.5 * np.cos(angulo), 0.5 * np.sin(angulo), 0],
                    color=color,
                    stroke_width=3
                )
                explosion.add(linea)

            explosion.move_to([x, y, 0])
            fuegos.add(explosion)

        return fuegos

# Comando para ejecutar la animación:
# manim -pqh pemdas_bodmas_animacion.py PEMDASBODMASAnimacion