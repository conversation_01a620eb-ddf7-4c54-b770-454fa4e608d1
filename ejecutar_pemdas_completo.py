#!/usr/bin/env python3
"""
Script para ejecutar la animación completa de PEMDAS/BODMAS.
Este script permite ejecutar la animación con diferentes calidades.
"""

import subprocess
import sys
import os

def ejecutar_animacion(calidad="media"):
    """
    Ejecutar la animación PEMDAS/BODMAS con la calidad especificada.
    
    Args:
        calidad (str): "alta", "media", o "baja"
    """
    
    # Mapear calidades a parámetros de Manim
    calidades = {
        "alta": "-pqh",      # Full HD, 60fps (puede requerir mucha memoria)
        "media": "-pqm",     # 720p, 30fps (recomendado)
        "baja": "-pql"       # 480p, 15fps (para pruebas rápidas)
    }
    
    if calidad not in calidades:
        print(f"Error: Calidad '{calidad}' no válida. Use: alta, media, o baja")
        return False
    
    # Verificar que el archivo existe
    archivo_animacion = "pemdas_bodmas_animacion.py"
    if not os.path.exists(archivo_animacion):
        print(f"Error: No se encontró el archivo {archivo_animacion}")
        return False
    
    # Comando para ejecutar Manim
    comando = [
        "manim",
        calidades[calidad],
        archivo_animacion,
        "PEMDASBODMASAnimacion"
    ]
    
    print(f"Ejecutando animación PEMDAS/BODMAS en calidad {calidad}...")
    print(f"Comando: {' '.join(comando)}")
    print("Esto puede tomar varios minutos dependiendo de la calidad...")
    print("-" * 60)
    
    try:
        # Ejecutar el comando
        resultado = subprocess.run(comando, check=True, capture_output=False)
        
        print("-" * 60)
        print("¡Animación completada exitosamente!")
        print("El archivo de video se encuentra en la carpeta 'media/videos/'")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"Error al ejecutar la animación: {e}")
        return False
    except FileNotFoundError:
        print("Error: Manim no está instalado o no se encuentra en el PATH")
        print("Instale Manim con: pip install manim")
        return False

def main():
    """Función principal del script"""
    print("=" * 60)
    print("ANIMACIÓN PEMDAS/BODMAS - EJECUTOR")
    print("=" * 60)
    
    # Verificar argumentos de línea de comandos
    if len(sys.argv) > 1:
        calidad = sys.argv[1].lower()
    else:
        # Preguntar al usuario por la calidad
        print("\nSeleccione la calidad de renderizado:")
        print("1. Alta (Full HD, 60fps) - Mejor calidad, más tiempo")
        print("2. Media (720p, 30fps) - Recomendado")
        print("3. Baja (480p, 15fps) - Para pruebas rápidas")
        
        while True:
            try:
                opcion = input("\nIngrese su opción (1-3): ").strip()
                if opcion == "1":
                    calidad = "alta"
                    break
                elif opcion == "2":
                    calidad = "media"
                    break
                elif opcion == "3":
                    calidad = "baja"
                    break
                else:
                    print("Opción no válida. Ingrese 1, 2, o 3.")
            except KeyboardInterrupt:
                print("\nOperación cancelada por el usuario.")
                return
    
    # Advertencia para calidad alta
    if calidad == "alta":
        print("\n⚠️  ADVERTENCIA: La calidad alta requiere mucha memoria RAM")
        print("   Si experimenta errores de memoria, use calidad media.")
        respuesta = input("¿Continuar? (s/n): ").lower()
        if respuesta not in ['s', 'si', 'y', 'yes']:
            print("Operación cancelada.")
            return
    
    # Ejecutar la animación
    exito = ejecutar_animacion(calidad)
    
    if exito:
        print("\n🎉 ¡Animación completada!")
        print("📁 Busque el archivo de video en la carpeta 'media/videos/'")
    else:
        print("\n❌ Error al generar la animación")
        print("💡 Sugerencias:")
        print("   - Verifique que Manim esté instalado: pip install manim")
        print("   - Pruebe con calidad más baja si hay problemas de memoria")
        print("   - Revise que el archivo pemdas_bodmas_animacion.py exista")

if __name__ == "__main__":
    main()
