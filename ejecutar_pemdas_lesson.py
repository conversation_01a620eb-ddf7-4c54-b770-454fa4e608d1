#!/usr/bin/env python3
"""
Script para ejecutar la lección de PEMDAS/BODMAS
Configurado para Full HD (1920x1080) a 60fps según las preferencias del usuario
"""

import os
import sys
from manim import *
from pemdas_bodmas_lesson import PEMDASBODMASLesson

def main():
    """Función principal para ejecutar la animación"""
    
    print("🎬 Iniciando la lección de PEMDAS/BODMAS...")
    print("📐 Configuración: Full HD (1920x1080) @ 60fps")
    print("🎨 Fuente personalizada: Font.otf")
    print("⏱️  Duración estimada: ~15 minutos")
    print("-" * 50)
    
    # Verificar que la fuente existe
    if not os.path.exists("Font.otf"):
        print("⚠️  Advertencia: Font.otf no encontrada en el directorio actual")
        print("   Se usará la fuente por defecto de Manim")
    else:
        print("✅ Fuente personalizada Font.otf encontrada")
    
    try:
        # Configurar Manim para Full HD 60fps
        config.pixel_height = 1080
        config.pixel_width = 1920
        config.frame_rate = 60
        config.background_color = "#0F0F23"  # Fondo oscuro
        
        # Configurar directorio de salida
        config.media_dir = "./media"
        
        # Configurar calidad
        config.quality = "high_quality"
        
        print("🚀 Ejecutando animación...")
        
        # Crear y ejecutar la escena
        scene = PEMDASBODMASLesson()
        scene.render()
        
        print("✅ ¡Animación completada exitosamente!")
        print(f"📁 Archivo de video guardado en: {config.media_dir}/videos/")
        
    except Exception as e:
        print(f"❌ Error durante la ejecución: {e}")
        print("💡 Sugerencias:")
        print("   - Verifica que Manim esté instalado correctamente")
        print("   - Asegúrate de que Font.otf esté en el directorio")
        print("   - Revisa que no haya errores de sintaxis en el código")
        sys.exit(1)

if __name__ == "__main__":
    main()
