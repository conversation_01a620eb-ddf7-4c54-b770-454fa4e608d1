# Lección de PEMDAS/BODMAS con Manim

## 📋 Descripción

Esta es una lección completa sobre la jerarquía de operaciones (PEMDAS/BODMAS) creada con Manim, diseñada para un público adulto con una duración aproximada de 15 minutos. La lección está optimizada para Full HD (1920x1080) a 60fps y utiliza una fuente personalizada.

## 🎯 Características Principales

- **Duración**: ~15 minutos
- **Resolución**: Full HD (1920x1080)
- **Frame Rate**: 60fps
- **Fuente**: Font.otf (personalizada)
- **Idioma**: Español
- **Público objetivo**: Adultos

## 📚 Estructura de la Lección

1. **Introducción Directa** (1 minuto)
   - Título y transformación del logo

2. **¿Por Qué Nos Importa PEMDAS/BODMAS?** (1 minuto)
   - Situaciones cotidianas
   - Comparación de resultados incorrectos vs correctos

3. **Desglosando el Acrónimo** (2 minutos)
   - Explicación de cada letra
   - Indicadores de dirección

4. **Ejemplo #1: Un Comienzo Sencillo** (2 minutos)
   - Expresión: 10 - 2 + 5

5. **Ejemplo #2: Multiplicación y División** (2 minutos)
   - Expresión: 6 + 4 × 2 ÷ 4

6. **Ejemplo #3: Paréntesis en Acción** (2 minutos)
   - Expresión: (6 + 4) × 2 ÷ 4

7. **Ejemplo #4: Elevando el Nivel** (3 minutos)
   - Expresión: 2³ + 10 ÷ 2 - 1

8. **Ejemplo #5: Aplicación en la Vida Real** (2 minutos)
   - Cálculo de precio con descuento e impuesto

9. **Conclusión** (1 minuto)
   - Mensaje final y animación de cierre

## 🎨 Esquema de Colores

- **P (Paréntesis)**: #FF5733 (Rojo-naranja)
- **E (Exponentes)**: #33FF57 (Verde)
- **M (Multiplicación)**: #5733FF (Púrpura)
- **D (División)**: #FFFF33 (Amarillo)
- **A (Adición)**: #33FFFF (Cian)
- **S (Sustracción)**: #FF33FF (Magenta)

## 📁 Estructura de Archivos

```
├── pemdas_bodmas_lesson.py    # Código principal de la lección
├── ejecutar_pemdas_lesson.py  # Script de ejecución
├── Font.otf                   # Fuente personalizada
├── README_PEMDAS_LESSON.md    # Este archivo
└── media/                     # Directorio de salida (se crea automáticamente)
```

## 🚀 Cómo Ejecutar

### Opción 1: Script de Ejecución (Recomendado)
```bash
python ejecutar_pemdas_lesson.py
```

### Opción 2: Comando Manim Directo
```bash
manim -pqh pemdas_bodmas_lesson.py PEMDASBODMASLesson
```

### Opción 3: Desde el Código
```python
from pemdas_bodmas_lesson import PEMDASBODMASLesson
scene = PEMDASBODMASLesson()
scene.render()
```

## 🔧 Personalización

### Modificar Texto
Todo el contenido de texto está centralizado en la clase `TextContent`. Para cambiar cualquier texto:

```python
class TextContent:
    MAIN_TITLE = "Tu Nuevo Título"
    # ... otros textos
```

### Modificar Matemáticas
Todas las expresiones matemáticas están en la clase `MathContent`:

```python
class MathContent:
    EXAMPLE_1_EXPRESSION = "tu_nueva_expresion"
    # ... otras expresiones
```

### Modificar Colores
Los colores están definidos en la clase `ColorScheme`:

```python
class ColorScheme:
    P_COLOR = "#TU_COLOR"
    # ... otros colores
```

## 📋 Requisitos

- Python 3.8+
- Manim Community Edition
- Font.otf (incluida)

### Instalación de Manim
```bash
pip install manim
```

## 🎬 Salida

El video se guardará en:
```
./media/videos/pemdas_bodmas_lesson/1080p60/PEMDASBODMASLesson.mp4
```

## 🔍 Características Técnicas

- **Separación de contenido**: Todo el texto y matemáticas están separados en clases para fácil modificación
- **Comentarios extensivos**: Cada sección está bien documentada
- **Funciones modulares**: Cada parte de la lección es una función separada
- **Colores consistentes**: Esquema de colores unificado en toda la lección
- **Animaciones suaves**: Transiciones optimizadas para mejor comprensión
- **Fuente personalizada**: Soporte para Font.otf

## 🛠️ Solución de Problemas

### Error: Font.otf no encontrada
- Asegúrate de que Font.otf esté en el mismo directorio que los archivos Python
- El código funcionará con fuente por defecto si no encuentra Font.otf

### Error de renderizado
- Verifica que Manim esté instalado correctamente
- Asegúrate de tener suficiente espacio en disco
- Revisa que no haya errores de sintaxis

### Calidad de video
- Para cambiar la calidad, modifica `config.quality` en el script de ejecución
- Opciones: "low_quality", "medium_quality", "high_quality", "fourk_quality"

## 📞 Soporte

Si encuentras algún problema o necesitas modificaciones, revisa:
1. Los comentarios en el código
2. La estructura modular de las clases
3. Las secciones claramente separadas para texto y matemáticas
