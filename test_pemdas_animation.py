#!/usr/bin/env python3
"""
Script de prueba para verificar que la animación PEMDAS/BODMAS funciona correctamente.
Este script ejecuta solo una sección pequeña para verificar que no hay errores de sintaxis.
"""

from manim import *
import numpy as np

# Configuración para prueba rápida (resolución reducida)
config.frame_width = 16
config.frame_height = 9
config.pixel_width = 854
config.pixel_height = 480
config.frame_rate = 30

class TestPEMDAS(Scene):
    def construct(self):
        # Configurar el color de fondo
        self.camera.background_color = "#0F0F23"

        # Prueba simple de la introducción
        titulo = Text(
            "¡Prueba de PEMDAS/BODMAS!",
            font_size=48,
            color=YELLOW,
            weight=BOLD
        )

        self.play(Write(titulo), run_time=2)

        # Prueba de ecuación matemática
        ecuacion = MathTex("2^3 + 5 \\times 2 - 1", font_size=60, color=WHITE)
        ecuacion.next_to(titulo, DOWN, buff=1)

        self.play(Write(ecuacion), run_time=1.5)

        # Resultado
        resultado = MathTex("= 17", font_size=70, color=GREEN)
        resultado.next_to(ecuacion, DOWN, buff=1)

        self.play(Write(resultado), run_time=1.5)

        # Mensaje de éxito
        exito = Text(
            "¡La animación funciona correctamente!",
            font_size=32,
            color=GREEN
        ).next_to(resultado, DOWN, buff=1)

        self.play(Write(exito), run_time=2)

        self.wait(2)

# Para ejecutar esta prueba:
# manim -pql test_pemdas_animation.py TestPEMDAS
