# Animación PEMDAS/BODMAS en Manim

## Descripción

Esta es una animación educativa completa que enseña el orden de operaciones PEMDAS/BODMAS en español. La animación está diseñada para ser atractiva, educativa y memorable para estudiantes de matemáticas.

## Características

- **Duración**: Aproximadamente 18 minutos
- **Resolución**: Full HD (1920x1080) a 60fps
- **Idioma**: Español
- **Contenido**: Explicación completa de PEMDAS/BODMAS con 5 ejemplos prácticos
- **Efectos visuales**: Animaciones suaves, colores vibrantes, efectos especiales

## Estructura del Contenido

1. **Introducción Entusiasta** (1 minuto)
   - Saludo motivacional
   - Presentación del tema como "superpoder matemático"

2. **Explicación PEMDAS/BODMAS** (2 minutos)
   - Desglose de cada componente
   - Visualización con colores y animaciones

3. **Ejemplo 1: Operaciones Básicas** (2 minutos)
   - Problema: 8 - 2 + 3 = 9
   - Demostración de orden izquierda a derecha

4. **Ejemplo 2: Prioridad de Multiplicación** (2 minutos)
   - Problema: 4 + 2 × 3 = 10
   - Énfasis en la prioridad de multiplicación

5. **Ejemplo 3: Poder de los Paréntesis** (2 minutos)
   - Problema: (4 + 2) × 3 = 18
   - Efectos especiales en paréntesis

6. **Ejemplo 4: Exponentes en Acción** (3 minutos)
   - Problema: 2³ + 5 × 2 - 1 = 17
   - Visualización 3D del exponente

7. **Ejemplo 5: Desafío Complejo** (3 minutos)
   - Problema: 12 ÷ (4 - 1) + 3² × 2 = 22
   - Aplicación completa de PEMDAS

8. **Mini Concurso Interactivo** (2 minutos)
   - Dos problemas con cronómetro
   - Revelación de respuestas

9. **Cierre Triunfante** (1 minuto)
   - Celebración con fuegos artificiales
   - Mensaje motivacional

## Requisitos

- Python 3.8+
- Manim Community Edition v0.19.0+
- NumPy

## Instalación

```bash
# Instalar Manim (si no está instalado)
pip install manim

# Verificar la instalación
manim --version
```

## Uso

### Renderizar la animación completa

#### Opción 1: Usar el script ejecutor (Recomendado)
```bash
# Ejecutar con interfaz interactiva
python ejecutar_pemdas_completo.py

# O especificar calidad directamente
python ejecutar_pemdas_completo.py media
```

#### Opción 2: Comando directo de Manim
```bash
# Calidad alta (Full HD, 60fps) - ⚠️ Requiere mucha RAM
manim -pqh pemdas_bodmas_animacion.py PEMDASBODMASAnimacion

# Calidad media (720p, 30fps) - Recomendado
manim -pqm pemdas_bodmas_animacion.py PEMDASBODMASAnimacion

# Calidad baja (480p, 15fps) - Para pruebas rápidas
manim -pql pemdas_bodmas_animacion.py PEMDASBODMASAnimacion
```

### Ejecutar prueba rápida

```bash
# Prueba de funcionalidad básica
manim -pql test_pemdas_animation.py TestPEMDAS
```

## Archivos Incluidos

- `pemdas_bodmas_animacion.py` - Animación principal completa
- `test_pemdas_animation.py` - Script de prueba
- `ejecutar_pemdas_completo.py` - Script ejecutor con interfaz amigable
- `README_PEMDAS_BODMAS.md` - Este archivo de documentación

## Personalización

### Modificar colores
Los colores están definidos usando las constantes de Manim:
- `YELLOW` - Títulos principales
- `BLUE` - Explicaciones
- `GREEN` - Resultados correctos
- `RED` - Multiplicación/División
- `PURPLE` - Paréntesis
- `ORANGE` - Operaciones activas

### Ajustar tiempos
Modifica los valores `run_time` en las animaciones para cambiar la velocidad.

### Cambiar resolución
Modifica las variables de configuración al inicio del archivo:
```python
config.pixel_width = 1920  # Ancho en píxeles
config.pixel_height = 1080  # Alto en píxeles
config.frame_rate = 60      # Frames por segundo
```

## Solución de Problemas

### Error de importación de Manim
```bash
pip install --upgrade manim
```

### Problemas de renderizado
- Asegúrate de tener suficiente espacio en disco
- Verifica que FFmpeg esté instalado correctamente
- Usa calidad baja (-pql) para pruebas

### Memoria insuficiente
- Reduce la resolución temporalmente
- Cierra otras aplicaciones
- Renderiza en secciones más pequeñas

## Contribuciones

Para mejorar la animación:
1. Modifica los métodos individuales para cada sección
2. Añade nuevos ejemplos creando métodos similares
3. Mejora los efectos visuales en los métodos auxiliares

## Licencia

Este proyecto es de uso educativo libre.
