from manim import *

# ===================================================================
# SECCIÓN DE CONFIGURACIÓN DE TEXTO Y MATEMÁTICAS
# ===================================================================

class TextContent:
    """Clase que contiene todo el contenido de texto para fácil sustitución"""

    # Títulos principales
    MAIN_TITLE = "PEMDAS/BODMAS"
    MAIN_SUBTITLE = ""  # Sin subtítulo

    # Sección: ¿Por qué nos importa?
    WHY_TITLE = "¿Por Qué Nos Importa PEMDAS/BODMAS?"
    SITUATION_1 = "Calcular descuentos"
    SITUATION_2 = "Dividir cuentas"
    SITUATION_3 = "Planificar presupuesto"
    SITUATION_4 = "Fórmulas financieras"

    # Explicaciones del acrónimo PEMDAS
    P_EXPLANATION = "Paréntesis - Primero"
    E_EXPLANATION = "Exponentes - Segundo"
    M_EXPLANATION = "Multiplicación - Tercero"
    D_EXPLANATION = "División - Tercero"
    A_EXPLANATION = "Adición - Cuarto"
    S_EXPLANATION = "Sustracción - Cuarto"

    # Explicaciones del acrónimo BODMAS
    B_EXPLANATION = "Brackets (Paréntesis) - Primero"
    O_EXPLANATION = "Orders (Exponentes) - Segundo"
    DM_EXPLANATION = "División y Multiplicación - Tercero"
    AS_EXPLANATION = "Adición y Sustracción - Cuarto"

    # Ejemplos
    EXAMPLE_1_TITLE = "Ejemplo #1: Un Comienzo Sencillo"
    EXAMPLE_2_TITLE = "Ejemplo #2: Multiplicación y División"
    EXAMPLE_3_TITLE = "Ejemplo #3: Paréntesis en Acción"
    EXAMPLE_4_TITLE = "Ejemplo #4: Elevando el Nivel"
    EXAMPLE_5_TITLE = "Ejemplo #5: Aplicación en la Vida Real"

    # Problema de la vida real
    REAL_LIFE_PROBLEM = "Calcular el costo total de una compra"
    ORIGINAL_PRICE = "Precio original: $100"
    DISCOUNT_TEXT = "Descuento del 20%"
    TAX_TEXT = "Impuesto del 10%"

    # Conclusión
    CONCLUSION_TITLE = "PEMDAS/BODMAS: Tu Herramienta para la Precisión"
    CONCLUSION_MESSAGE = "Practica, aplica y domina la jerarquía de operaciones."
    CONCLUSION_SUBMESSAGE = "¡Te servirá en muchas situaciones!"

class MathContent:
    """Clase que contiene todas las expresiones matemáticas para fácil sustitución"""

    # Acrónimos
    PEMDAS = "PEMDAS"
    BODMAS = "BODMAS"

    # Ejemplos matemáticos
    EXAMPLE_1_EXPRESSION = "10 - 2 + 5"
    EXAMPLE_1_STEP_1 = "10 - 2 = 8"
    EXAMPLE_1_STEP_2 = "8 + 5 = 13"
    EXAMPLE_1_SOLUTION = "13"

    EXAMPLE_2_EXPRESSION = "6 + 4 \\times 2 \\div 4"
    EXAMPLE_2_STEP_1 = "4 \\times 2 = 8"
    EXAMPLE_2_STEP_2 = "8 \\div 4 = 2"
    EXAMPLE_2_STEP_3 = "6 + 2 = 8"
    EXAMPLE_2_SOLUTION = "8"

    EXAMPLE_3_EXPRESSION = "(6 + 4) \\times 2 \\div 4"
    EXAMPLE_3_STEP_1 = "6 + 4 = 10"
    EXAMPLE_3_STEP_2 = "10 \\times 2 = 20"
    EXAMPLE_3_STEP_3 = "20 \\div 4 = 5"
    EXAMPLE_3_SOLUTION = "5"

    EXAMPLE_4_EXPRESSION = "2^3 + 10 \\div 2 - 1"
    EXAMPLE_4_STEP_1 = "2^3 = 8"
    EXAMPLE_4_STEP_2 = "10 \\div 2 = 5"
    EXAMPLE_4_STEP_3 = "8 + 5 = 13"
    EXAMPLE_4_STEP_4 = "13 - 1 = 12"
    EXAMPLE_4_SOLUTION = "12"

    # Ejemplo de la vida real
    DISCOUNT_CALC = "100 \\times 0.20 = 20"
    PRICE_AFTER_DISCOUNT = "100 - 20 = 80"
    TAX_CALC = "80 \\times 0.10 = 8"
    FINAL_PRICE = "80 + 8 = 88"
    REAL_LIFE_SOLUTION = "\\$88"

# ===================================================================
# CONFIGURACIÓN DE COLORES Y ESTILOS
# ===================================================================

class ColorScheme:
    """Esquema de colores consistente para toda la lección"""

    # Colores para PEMDAS
    P_COLOR = "#FF5733"  # Rojo-naranja
    E_COLOR = "#33FF57"  # Verde
    M_COLOR = "#5733FF"  # Púrpura
    D_COLOR = "#FFFF33"  # Amarillo
    A_COLOR = "#33FFFF"  # Cian
    S_COLOR = "#FF33FF"  # Magenta

    # Colores para BODMAS
    B_COLOR = "#FF6B35"  # Naranja
    O_COLOR = "#4ECDC4"  # Turquesa
    DM_COLOR = "#45B7D1"  # Azul claro
    AS_COLOR = "#96CEB4"  # Verde menta

    # Colores generales
    BACKGROUND = "#0F0F23"
    TEXT_PRIMARY = "#FFFFFF"
    TEXT_SECONDARY = "#CCCCCC"
    HIGHLIGHT = "#FFD700"
    SUCCESS = "#00FF00"
    ERROR = "#FF0000"

# ===================================================================
# CONFIGURACIÓN DE FUENTES
# ===================================================================

def setup_font():
    """Configura la fuente personalizada"""
    # La fuente correcta es "Cera Round Pro" según el sistema
    font_name = "Cera Round Pro"
    return font_name

# ===================================================================
# CLASE PRINCIPAL DE LA LECCIÓN
# ===================================================================

class PEMDASBODMASLesson(Scene):
    """Clase principal que contiene toda la lección de PEMDAS/BODMAS"""

    def construct(self):
        """Método principal que ejecuta toda la lección"""

        # Configurar fuente personalizada
        self.font_name = setup_font()

        # Ejecutar todas las secciones de la lección
        self.introduction()
        self.why_it_matters()
        self.breakdown_acronym()
        self.example_1()
        self.example_2()
        self.example_3()
        self.example_4()
        self.example_5()
        self.conclusion()

    def create_text(self, text, **kwargs):
        """Método auxiliar para crear texto con fuente personalizada"""
        return Text(text, font=self.font_name, **kwargs)

    def create_math(self, expression, **kwargs):
        """Método auxiliar para crear expresiones matemáticas"""
        return MathTex(expression, **kwargs)

    def smooth_transition(self):
        """Transición suave entre secciones con fade out y pausa"""
        # Fade out de todos los objetos en pantalla
        if self.mobjects:
            self.play(
                *[FadeOut(mob) for mob in self.mobjects],
                run_time=1
            )

        # Pausa de 1 segundo
        self.wait(1)

    # ===================================================================
    # SECCIÓN 1: INTRODUCCIÓN DIRECTA (1 minuto)
    # ===================================================================

    def introduction(self):
        """Introducción con título y efecto de zoom con desvanecimiento"""

        # Crear título principal centrado
        title = self.create_text(
            TextContent.MAIN_TITLE,
            font_size=64,
            color=ColorScheme.TEXT_PRIMARY
        )
        title.move_to(ORIGIN)

        # Animación de entrada del título
        self.play(
            Write(title),
            run_time=2
        )

        # Esperar 1 segundo
        self.wait(1)

        # Efecto de zoom con desvanecimiento (1 segundo)
        self.play(
            title.animate.scale(3).set_opacity(0),
            run_time=1
        )

        # Pausa de 1 segundo antes de la siguiente sección
        self.wait(1)

    # ===================================================================
    # SECCIÓN 2: ¿POR QUÉ NOS IMPORTA? (1 minuto)
    # ===================================================================

    def why_it_matters(self):
        """Muestra por qué es importante PEMDAS/BODMAS en la vida real"""

        # Título de la sección
        section_title = self.create_text(
            TextContent.WHY_TITLE,
            font_size=40,
            color=ColorScheme.TEXT_PRIMARY
        )
        section_title.to_edge(UP)

        self.play(Write(section_title), run_time=1)

        # Situaciones cotidianas
        situations = [
            TextContent.SITUATION_1,
            TextContent.SITUATION_2,
            TextContent.SITUATION_3,
            TextContent.SITUATION_4
        ]

        # Crear y animar cada situación
        for i, situation in enumerate(situations):
            situation_text = self.create_text(
                situation,
                font_size=28,
                color=ColorScheme.TEXT_SECONDARY
            )
            situation_text.shift(UP * (1 - i * 0.8))

            self.play(
                FadeIn(situation_text),
                run_time=0.8
            )

            if i < len(situations) - 1:
                self.wait(0.5)

        self.wait(2)
        self.smooth_transition()

    # ===================================================================
    # SECCIÓN 3: DESGLOSANDO LOS ACRÓNIMOS (4 minutos)
    # ===================================================================

    def breakdown_acronym(self):
        """Explica cada letra de los acrónimos PEMDAS y BODMAS"""

        # PARTE 1: PEMDAS (2 minutos)
        self.show_pemdas_breakdown()

        # PARTE 2: BODMAS (2 minutos)
        self.show_bodmas_breakdown()

    def show_pemdas_breakdown(self):
        """Muestra el desglose de PEMDAS"""

        # Título PEMDAS
        pemdas_title = self.create_text(
            "PEMDAS",
            font_size=48,
            color=ColorScheme.HIGHLIGHT
        )
        pemdas_title.to_edge(UP)

        self.play(Write(pemdas_title), run_time=1)

        # Mostrar PEMDAS verticalmente
        letters = ["P", "E", "M", "D", "A", "S"]
        colors = [
            ColorScheme.P_COLOR,
            ColorScheme.E_COLOR,
            ColorScheme.M_COLOR,
            ColorScheme.D_COLOR,
            ColorScheme.A_COLOR,
            ColorScheme.S_COLOR
        ]

        explanations = [
            TextContent.P_EXPLANATION,
            TextContent.E_EXPLANATION,
            TextContent.M_EXPLANATION,
            TextContent.D_EXPLANATION,
            TextContent.A_EXPLANATION,
            TextContent.S_EXPLANATION
        ]

        # Crear las letras verticalmente
        letter_group = VGroup()
        explanation_group = VGroup()

        for i, (letter, color, explanation) in enumerate(zip(letters, colors, explanations)):
            # Letra del acrónimo
            letter_text = self.create_text(
                letter,
                font_size=50,
                color=color
            )
            letter_text.shift(UP * (2 - i * 0.6))
            letter_text.shift(LEFT * 3)

            # Explicación
            explanation_text = self.create_text(
                explanation,
                font_size=28,
                color=ColorScheme.TEXT_SECONDARY
            )
            explanation_text.next_to(letter_text, RIGHT, buff=1)

            letter_group.add(letter_text)
            explanation_group.add(explanation_text)

        # Animar la aparición de cada letra y explicación
        for i in range(len(letters)):
            self.play(
                Write(letter_group[i]),
                FadeIn(explanation_group[i]),
                run_time=0.6
            )
            self.wait(0.3)

        self.wait(2)
        self.smooth_transition()

    def show_bodmas_breakdown(self):
        """Muestra el desglose de BODMAS"""

        # Título BODMAS
        bodmas_title = self.create_text(
            "BODMAS",
            font_size=48,
            color=ColorScheme.SUCCESS
        )
        bodmas_title.to_edge(UP)

        self.play(Write(bodmas_title), run_time=1)

        # Mostrar BODMAS verticalmente
        letters = ["B", "O", "DM", "AS"]
        colors = [
            ColorScheme.B_COLOR,
            ColorScheme.O_COLOR,
            ColorScheme.DM_COLOR,
            ColorScheme.AS_COLOR
        ]

        explanations = [
            TextContent.B_EXPLANATION,
            TextContent.O_EXPLANATION,
            TextContent.DM_EXPLANATION,
            TextContent.AS_EXPLANATION
        ]

        # Crear las letras verticalmente
        letter_group = VGroup()
        explanation_group = VGroup()

        for i, (letter, color, explanation) in enumerate(zip(letters, colors, explanations)):
            # Letra del acrónimo
            letter_text = self.create_text(
                letter,
                font_size=50,
                color=color
            )
            letter_text.shift(UP * (1.5 - i * 0.8))
            letter_text.shift(LEFT * 3)

            # Explicación
            explanation_text = self.create_text(
                explanation,
                font_size=28,
                color=ColorScheme.TEXT_SECONDARY
            )
            explanation_text.next_to(letter_text, RIGHT, buff=1)

            letter_group.add(letter_text)
            explanation_group.add(explanation_text)

        # Animar la aparición de cada letra y explicación
        for i in range(len(letters)):
            self.play(
                Write(letter_group[i]),
                FadeIn(explanation_group[i]),
                run_time=0.8
            )
            self.wait(0.4)

        # Mostrar nota sobre el orden
        note_text = self.create_text(
            "Mismo orden de operaciones, diferente notación",
            font_size=24,
            color=ColorScheme.HIGHLIGHT
        )
        note_text.shift(DOWN * 2.5)

        self.play(Write(note_text), run_time=1.5)
        self.wait(2)
        self.smooth_transition()

    # ===================================================================
    # SECCIÓN 4: EJEMPLO #1 - UN COMIENZO SENCILLO (2 minutos)
    # ===================================================================

    def example_1(self):
        """Primer ejemplo: 10 - 2 + 5"""

        # Título del ejemplo
        title = self.create_text(
            TextContent.EXAMPLE_1_TITLE,
            font_size=36,
            color=ColorScheme.TEXT_PRIMARY
        )
        title.to_edge(UP)

        self.play(Write(title), run_time=1)

        # Expresión original
        expression = self.create_math(
            MathContent.EXAMPLE_1_EXPRESSION,
            font_size=48,
            color=ColorScheme.TEXT_PRIMARY
        )
        expression.shift(UP * 1)

        self.play(Write(expression), run_time=1)
        self.wait(1)

        # Paso 1: 10 - 2 = 8
        step1 = self.create_math(
            MathContent.EXAMPLE_1_STEP_1,
            font_size=40,
            color=ColorScheme.HIGHLIGHT
        )
        step1.shift(DOWN * 0.5)

        # Resaltar la operación 10 - 2
        highlight_box = SurroundingRectangle(
            expression[0:3],  # "10 - 2"
            color=ColorScheme.HIGHLIGHT,
            buff=0.1
        )

        self.play(
            Create(highlight_box),
            run_time=1
        )

        self.play(
            Write(step1),
            run_time=1
        )

        self.wait(1)

        # Paso 2: 8 + 5 = 13
        step2 = self.create_math(
            MathContent.EXAMPLE_1_STEP_2,
            font_size=40,
            color=ColorScheme.HIGHLIGHT
        )
        step2.next_to(step1, DOWN, buff=0.5)

        self.play(
            FadeOut(highlight_box),
            Write(step2),
            run_time=1
        )

        self.wait(1)

        # Solución final
        solution = self.create_math(
            f"\\text{{Solución: }} {MathContent.EXAMPLE_1_SOLUTION}",
            font_size=44,
            color=ColorScheme.SUCCESS
        )
        solution.next_to(step2, DOWN, buff=1)

        self.play(
            Write(solution),
            run_time=1
        )

        self.wait(2)
        self.smooth_transition()

    # ===================================================================
    # SECCIÓN 5: EJEMPLO #2 - MULTIPLICACIÓN Y DIVISIÓN (2 minutos)
    # ===================================================================

    def example_2(self):
        """Segundo ejemplo: 6 + 4 × 2 ÷ 4"""

        # Título del ejemplo
        title = self.create_text(
            TextContent.EXAMPLE_2_TITLE,
            font_size=36,
            color=ColorScheme.TEXT_PRIMARY
        )
        title.to_edge(UP)

        self.play(Write(title), run_time=1)

        # Expresión original
        expression = self.create_math(
            MathContent.EXAMPLE_2_EXPRESSION,
            font_size=48,
            color=ColorScheme.TEXT_PRIMARY
        )
        expression.shift(UP * 1)

        self.play(Write(expression), run_time=1)
        self.wait(1)

        # Paso 1: 4 × 2 = 8
        step1 = self.create_math(
            MathContent.EXAMPLE_2_STEP_1,
            font_size=40,
            color=ColorScheme.M_COLOR
        )
        step1.shift(DOWN * 0.5)

        self.play(Write(step1), run_time=1)
        self.wait(1)

        # Paso 2: 8 ÷ 4 = 2
        step2 = self.create_math(
            MathContent.EXAMPLE_2_STEP_2,
            font_size=40,
            color=ColorScheme.D_COLOR
        )
        step2.next_to(step1, DOWN, buff=0.5)

        self.play(Write(step2), run_time=1)
        self.wait(1)

        # Paso 3: 6 + 2 = 8
        step3 = self.create_math(
            MathContent.EXAMPLE_2_STEP_3,
            font_size=40,
            color=ColorScheme.A_COLOR
        )
        step3.next_to(step2, DOWN, buff=0.5)

        self.play(Write(step3), run_time=1)
        self.wait(1)

        # Solución final
        solution = self.create_math(
            f"\\text{{Solución: }} {MathContent.EXAMPLE_2_SOLUTION}",
            font_size=44,
            color=ColorScheme.SUCCESS
        )
        solution.next_to(step3, DOWN, buff=1)

        self.play(Write(solution), run_time=1)
        self.wait(2)
        self.smooth_transition()

    # ===================================================================
    # SECCIÓN 6: EJEMPLO #3 - PARÉNTESIS EN ACCIÓN (2 minutos)
    # ===================================================================

    def example_3(self):
        """Tercer ejemplo: (6 + 4) × 2 ÷ 4"""

        # Título del ejemplo
        title = self.create_text(
            TextContent.EXAMPLE_3_TITLE,
            font_size=36,
            color=ColorScheme.TEXT_PRIMARY
        )
        title.to_edge(UP)

        self.play(Write(title), run_time=1)

        # Expresión original
        expression = self.create_math(
            MathContent.EXAMPLE_3_EXPRESSION,
            font_size=48,
            color=ColorScheme.TEXT_PRIMARY
        )
        expression.shift(UP * 1)

        self.play(Write(expression), run_time=1)
        self.wait(1)

        # Resaltar paréntesis
        parentheses_box = SurroundingRectangle(
            expression[0:5],  # "(6 + 4)"
            color=ColorScheme.P_COLOR,
            buff=0.1
        )

        self.play(Create(parentheses_box), run_time=1)

        # Paso 1: 6 + 4 = 10
        step1 = self.create_math(
            MathContent.EXAMPLE_3_STEP_1,
            font_size=40,
            color=ColorScheme.P_COLOR
        )
        step1.shift(DOWN * 0.5)

        self.play(Write(step1), run_time=1)
        self.wait(1)

        self.play(FadeOut(parentheses_box), run_time=0.5)

        # Paso 2: 10 × 2 = 20
        step2 = self.create_math(
            MathContent.EXAMPLE_3_STEP_2,
            font_size=40,
            color=ColorScheme.M_COLOR
        )
        step2.next_to(step1, DOWN, buff=0.5)

        self.play(Write(step2), run_time=1)
        self.wait(1)

        # Paso 3: 20 ÷ 4 = 5
        step3 = self.create_math(
            MathContent.EXAMPLE_3_STEP_3,
            font_size=40,
            color=ColorScheme.D_COLOR
        )
        step3.next_to(step2, DOWN, buff=0.5)

        self.play(Write(step3), run_time=1)
        self.wait(1)

        # Solución final
        solution = self.create_math(
            f"\\text{{Solución: }} {MathContent.EXAMPLE_3_SOLUTION}",
            font_size=44,
            color=ColorScheme.SUCCESS
        )
        solution.next_to(step3, DOWN, buff=1)

        self.play(Write(solution), run_time=1)
        self.wait(2)
        self.smooth_transition()

    # ===================================================================
    # SECCIÓN 7: EJEMPLO #4 - ELEVANDO EL NIVEL (3 minutos)
    # ===================================================================

    def example_4(self):
        """Cuarto ejemplo: 2³ + 10 ÷ 2 - 1"""

        # Título del ejemplo
        title = self.create_text(
            TextContent.EXAMPLE_4_TITLE,
            font_size=36,
            color=ColorScheme.TEXT_PRIMARY
        )
        title.to_edge(UP)

        self.play(Write(title), run_time=1)

        # Expresión original
        expression = self.create_math(
            MathContent.EXAMPLE_4_EXPRESSION,
            font_size=48,
            color=ColorScheme.TEXT_PRIMARY
        )
        expression.shift(UP * 1)

        self.play(Write(expression), run_time=1)
        self.wait(1)

        # Paso 1: 2³ = 8
        step1 = self.create_math(
            MathContent.EXAMPLE_4_STEP_1,
            font_size=40,
            color=ColorScheme.E_COLOR
        )
        step1.shift(DOWN * 0.5)

        self.play(Write(step1), run_time=1)
        self.wait(1)

        # Paso 2: 10 ÷ 2 = 5
        step2 = self.create_math(
            MathContent.EXAMPLE_4_STEP_2,
            font_size=40,
            color=ColorScheme.D_COLOR
        )
        step2.next_to(step1, DOWN, buff=0.5)

        self.play(Write(step2), run_time=1)
        self.wait(1)

        # Paso 3: 8 + 5 = 13
        step3 = self.create_math(
            MathContent.EXAMPLE_4_STEP_3,
            font_size=40,
            color=ColorScheme.A_COLOR
        )
        step3.next_to(step2, DOWN, buff=0.5)

        self.play(Write(step3), run_time=1)
        self.wait(1)

        # Paso 4: 13 - 1 = 12
        step4 = self.create_math(
            MathContent.EXAMPLE_4_STEP_4,
            font_size=40,
            color=ColorScheme.S_COLOR
        )
        step4.next_to(step3, DOWN, buff=0.5)

        self.play(Write(step4), run_time=1)
        self.wait(1)

        # Solución final
        solution = self.create_math(
            f"\\text{{Solución: }} {MathContent.EXAMPLE_4_SOLUTION}",
            font_size=44,
            color=ColorScheme.SUCCESS
        )
        solution.next_to(step4, DOWN, buff=1)

        self.play(Write(solution), run_time=1)
        self.wait(2)
        self.smooth_transition()

    # ===================================================================
    # SECCIÓN 8: EJEMPLO #5 - APLICACIÓN EN LA VIDA REAL (2 minutos)
    # ===================================================================

    def example_5(self):
        """Quinto ejemplo: Aplicación práctica con descuentos e impuestos"""

        # Título del ejemplo
        title = self.create_text(
            TextContent.EXAMPLE_5_TITLE,
            font_size=36,
            color=ColorScheme.TEXT_PRIMARY
        )
        title.to_edge(UP)

        self.play(Write(title), run_time=1)

        # Descripción del problema
        problem_desc = self.create_text(
            TextContent.REAL_LIFE_PROBLEM,
            font_size=28,
            color=ColorScheme.TEXT_SECONDARY
        )
        problem_desc.shift(UP * 2)

        # Detalles del problema
        original_price = self.create_text(
            TextContent.ORIGINAL_PRICE,
            font_size=24,
            color=ColorScheme.TEXT_SECONDARY
        )
        original_price.shift(UP * 1.5)

        discount_text = self.create_text(
            TextContent.DISCOUNT_TEXT,
            font_size=24,
            color=ColorScheme.TEXT_SECONDARY
        )
        discount_text.shift(UP * 1.2)

        tax_text = self.create_text(
            TextContent.TAX_TEXT,
            font_size=24,
            color=ColorScheme.TEXT_SECONDARY
        )
        tax_text.shift(UP * 0.9)

        self.play(
            Write(problem_desc),
            Write(original_price),
            Write(discount_text),
            Write(tax_text),
            run_time=2
        )
        self.wait(1)

        # Paso 1: Calcular descuento
        step1 = self.create_math(
            MathContent.DISCOUNT_CALC,
            font_size=36,
            color=ColorScheme.HIGHLIGHT
        )
        step1.shift(UP * 0.3)

        self.play(Write(step1), run_time=1)
        self.wait(1)

        # Paso 2: Precio después del descuento
        step2 = self.create_math(
            MathContent.PRICE_AFTER_DISCOUNT,
            font_size=36,
            color=ColorScheme.HIGHLIGHT
        )
        step2.next_to(step1, DOWN, buff=0.4)

        self.play(Write(step2), run_time=1)
        self.wait(1)

        # Paso 3: Calcular impuesto
        step3 = self.create_math(
            MathContent.TAX_CALC,
            font_size=36,
            color=ColorScheme.HIGHLIGHT
        )
        step3.next_to(step2, DOWN, buff=0.4)

        self.play(Write(step3), run_time=1)
        self.wait(1)

        # Paso 4: Precio final
        step4 = self.create_math(
            MathContent.FINAL_PRICE,
            font_size=36,
            color=ColorScheme.HIGHLIGHT
        )
        step4.next_to(step3, DOWN, buff=0.4)

        self.play(Write(step4), run_time=1)
        self.wait(1)

        # Solución final
        solution = self.create_math(
            f"\\text{{Precio Final: }} {MathContent.REAL_LIFE_SOLUTION}",
            font_size=44,
            color=ColorScheme.SUCCESS
        )
        solution.next_to(step4, DOWN, buff=1)

        self.play(Write(solution), run_time=1)
        self.wait(3)
        self.smooth_transition()

    # ===================================================================
    # SECCIÓN 9: CONCLUSIÓN (1 minuto)
    # ===================================================================

    def conclusion(self):
        """Conclusión de la lección"""

        # Título de conclusión
        conclusion_title = self.create_text(
            TextContent.CONCLUSION_TITLE,
            font_size=40,
            color=ColorScheme.TEXT_PRIMARY
        )
        conclusion_title.shift(UP * 1)

        self.play(Write(conclusion_title), run_time=2)

        # Mensaje principal
        main_message = self.create_text(
            TextContent.CONCLUSION_MESSAGE,
            font_size=28,
            color=ColorScheme.TEXT_SECONDARY
        )
        main_message.shift(DOWN * 0.5)

        # Submensaje
        sub_message = self.create_text(
            TextContent.CONCLUSION_SUBMESSAGE,
            font_size=28,
            color=ColorScheme.HIGHLIGHT
        )
        sub_message.next_to(main_message, DOWN, buff=0.5)

        self.play(
            Write(main_message),
            run_time=1.5
        )

        self.play(
            Write(sub_message),
            run_time=1.5
        )

        self.wait(1)

        # Animación final: calculadora transformándose en símbolo de infinito
        calculator = Rectangle(
            width=2,
            height=1.5,
            color=ColorScheme.HIGHLIGHT,
            fill_opacity=0.3
        )
        calculator.shift(DOWN * 2)

        # Botones de la calculadora
        buttons = VGroup()
        for i in range(3):
            for j in range(3):
                button = Circle(radius=0.1, color=ColorScheme.TEXT_SECONDARY)
                button.shift(
                    calculator.get_center() +
                    RIGHT * (j - 1) * 0.3 +
                    UP * (i - 1) * 0.2
                )
                buttons.add(button)

        calculator_group = VGroup(calculator, buttons)

        self.play(Create(calculator_group), run_time=1)
        self.wait(1)

        # Símbolo de infinito
        infinity = self.create_math(
            "\\infty",
            font_size=72,
            color=ColorScheme.HIGHLIGHT
        )
        infinity.shift(DOWN * 2)

        self.play(
            Transform(calculator_group, infinity),
            run_time=2
        )

        self.wait(3)

        # Fade out final
        self.play(
            *[FadeOut(mob) for mob in self.mobjects],
            run_time=2
        )

        self.wait(1)

# ===================================================================
# CONFIGURACIÓN PARA EJECUTAR LA ANIMACIÓN
# ===================================================================

if __name__ == "__main__":
    # Configuración de calidad para Full HD 60fps
    config.pixel_height = 1080
    config.pixel_width = 1920
    config.frame_rate = 60

    # Ejecutar la escena
    scene = PEMDASBODMASLesson()
    scene.render()